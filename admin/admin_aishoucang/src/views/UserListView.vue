<template>
  <div class="user-list-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><User /></el-icon>
            用户管理
          </h1>
          <p class="page-subtitle">管理系统中的所有用户信息，查看用户详情和操作记录</p>
        </div>
        <div class="header-stats">
          <div class="stat-card">
            <div class="stat-number">{{ total }}</div>
            <div class="stat-label">总用户数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ userList.length }}</div>
            <div class="stat-label">当前页用户</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="toolbar-card">
      <div class="toolbar-content">
        <div class="search-section">
          <div class="search-wrapper">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户手机号或昵称..."
              class="search-input"
              size="large"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>
            <el-button
              type="primary"
              size="large"
              class="search-btn"
              @click="handleSearch"
            >
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
          </div>
        </div>
        <div class="action-section">
          <el-button
            size="large"
            class="refresh-btn"
            @click="refreshData"
          >
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button
            type="success"
            size="large"
            class="export-btn"
          >
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户列表表格 -->
    <div class="table-card">
      <div class="table-header">
        <h3 class="table-title">用户列表</h3>
        <div class="table-actions">
          <el-tag :type="loading ? 'info' : 'success'" size="small">
            {{ loading ? '加载中...' : `共 ${total} 条记录` }}
          </el-tag>
        </div>
      </div>
      <div class="table-content">
        <el-table
          :data="userList"
          v-loading="loading"
          class="modern-table"
          :header-cell-style="tableHeaderStyle"
          :row-style="tableRowStyle"
          empty-text="暂无用户数据"
          element-loading-text="正在加载用户数据..."
          element-loading-spinner="el-icon-loading"
        >
          <el-table-column prop="id" label="ID" width="80" align="center">
            <template #default="scope">
              <el-tag size="small" type="info">#{{ scope.row.id }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号" width="140" align="center">
            <template #default="scope">
              <div class="phone-cell">
                <el-icon class="phone-icon"><Phone /></el-icon>
                <span class="phone-text">{{ scope.row.phone }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="nickname" label="昵称" width="150">
            <template #default="scope">
              <div class="nickname-cell">
                <span class="nickname-text">{{ scope.row.nickname || '未设置昵称' }}</span>
                <el-tag v-if="!scope.row.nickname" size="mini" type="warning">未完善</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="头像" width="100" align="center">
            <template #default="scope">
              <div class="avatar-cell">
                <el-avatar
                  :size="50"
                  :src="scope.row.avatar"
                  class="user-avatar"
                >
                  <el-icon class="avatar-icon"><User /></el-icon>
                </el-avatar>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="注册时间" width="200">
            <template #default="scope">
              <div class="time-cell">
                <el-icon class="time-icon"><Calendar /></el-icon>
                <div class="time-content">
                  <div class="time-main">{{ formatDate(scope.row.created_at) }}</div>
                  <div class="time-sub">{{ getRelativeTime(scope.row.created_at) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="最后活跃" width="200">
            <template #default="scope">
              <div class="time-cell">
                <el-icon class="time-icon"><Clock /></el-icon>
                <div class="time-content">
                  <div class="time-main">{{ formatDate(scope.row.updated_at) }}</div>
                  <div class="time-sub">{{ getRelativeTime(scope.row.updated_at) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="scope">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  class="action-btn"
                  @click="viewUser(scope.row)"
                >
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  class="action-btn"
                  @click="editUser(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-card">
      <div class="pagination-info">
        <span class="pagination-text">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, total) }} 条，
          共 {{ total }} 条记录
        </span>
      </div>
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="sizes, prev, pager, next, jumper"
        class="modern-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search, Refresh, User, Phone, Calendar, Clock,
  View, Edit, Download
} from '@element-plus/icons-vue'
import { getUserList } from '@/api/user'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(50)  // 修改默认分页大小为50
const searchKeyword = ref('')

// 表格样式配置
const tableHeaderStyle = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: '#ffffff',
  fontWeight: '600',
  fontSize: '14px',
  height: '60px'
}

const tableRowStyle = ({ rowIndex }) => {
  return {
    height: '70px',
    backgroundColor: rowIndex % 2 === 0 ? '#fafbfc' : '#ffffff'
  }
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const response = await getUserList({
      page: currentPage.value,
      page_size: pageSize.value
    })
    
    if (response.code === 0) {
      userList.value = response.data.users
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchUserList()
}

// 刷新数据
const refreshData = () => {
  fetchUserList()
}

// 分页大小改变
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchUserList()
}

// 当前页改变
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchUserList()
}

// 查看用户
const viewUser = (user) => {
  ElMessage.info(`查看用户: ${user.phone}`)
}

// 编辑用户
const editUser = (user) => {
  ElMessage.info(`编辑用户: ${user.phone}`)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取相对时间
const getRelativeTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  return '很久以前'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUserList()
})
</script>

<style scoped>
.user-list-container {
  padding: 32px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  color: white;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  min-width: 120px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* 工具栏样式 */
.toolbar-card {
  margin-bottom: 24px;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.search-section {
  flex: 1;
  max-width: 600px;
}

.search-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  flex: 1;
  max-width: 400px;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.search-icon {
  color: #9ca3af;
}

.search-btn, .refresh-btn, .export-btn {
  border-radius: 12px;
  font-weight: 600;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.refresh-btn {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  border: 1px solid #d1d5db;
}

.refresh-btn:hover {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  transform: translateY(-1px);
}

.export-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.action-section {
  display: flex;
  gap: 16px;
}

/* 表格卡片样式 */
.table-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.table-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-content {
  padding: 0;
}

/* 现代化表格样式 */
.modern-table {
  width: 100%;
}

.modern-table :deep(.el-table__header-wrapper) {
  border-radius: 0;
}

.modern-table :deep(.el-table__body-wrapper) {
  border-radius: 0;
}

.modern-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.modern-table :deep(.el-table__row:hover) {
  background-color: #f8fafc !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 表格单元格样式 */
.phone-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.phone-icon {
  color: #667eea;
  font-size: 16px;
}

.phone-text {
  font-weight: 500;
  color: #374151;
}

.nickname-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nickname-text {
  font-weight: 500;
  color: #374151;
}

.avatar-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: 3px solid #f3f4f6;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
}

.avatar-icon {
  font-size: 24px;
  color: white;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-icon {
  color: #6b7280;
  font-size: 16px;
  flex-shrink: 0;
}

.time-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-main {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.time-sub {
  font-size: 12px;
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 16px;
}

.action-btn:hover {
  transform: translateY(-1px);
}

.action-btn.el-button--primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.action-btn.el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.action-btn.el-button--warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border: none;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.action-btn.el-button--warning:hover {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

/* 分页卡片样式 */
.pagination-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info {
  flex: 1;
}

.pagination-text {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.modern-pagination {
  flex-shrink: 0;
}

.modern-pagination :deep(.el-pagination__sizes) {
  margin-right: 16px;
}

.modern-pagination :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.el-select .el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.modern-pagination :deep(.btn-prev),
.modern-pagination :deep(.btn-next) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: white;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.btn-prev:hover),
.modern-pagination :deep(.btn-next:hover) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
  transform: translateY(-1px);
}

.modern-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 4px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.el-pager li:hover) {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  transform: translateY(-1px);
}

.modern-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modern-pagination :deep(.el-pagination__jump) {
  margin-left: 16px;
}

.modern-pagination :deep(.el-pagination__jump .el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.el-pagination__jump .el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 20px;
  }

  .search-wrapper {
    width: 100%;
    justify-content: center;
  }

  .pagination-card {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .user-list-container {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .header-content {
    padding: 24px;
  }

  .toolbar-content {
    padding: 16px;
  }

  .table-header {
    padding: 16px 20px;
  }

  .pagination-card {
    padding: 16px 20px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
